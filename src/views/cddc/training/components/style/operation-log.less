// 操作日志组件样式
.operation-log {
  display: flex;
  flex-direction: column;
  gap: 0.74vw; // 12px * 0.0702 * 0.66

  :deep(.screen-box) {
    flex: 1;
    padding: 0.74vw; // 16px * 0.0702 * 0.66
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 28.2vh; // 446px * 0.1053 * 0.66
  }
  &.simple{
    flex:1;
    :deep(.screen-box) {
      max-height: 100%;
    }
  }

  .log-list {
    display: flex;
    flex-direction: column;
    gap: 0.37vw; // 8px * 0.0702 * 0.66
    height: 100%;

    &.box-content {
      display: flex;
      flex-direction: column;
      gap: 0.37vw; // 20px * 0.0702 * 0.66
      position: relative;
      z-index: 1;
      overflow-x: hidden;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;

      &::-webkit-scrollbar {
        width: 0.28vw; // 6px * 0.0702 * 0.66
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 0.14vw; // 3px * 0.0702 * 0.66
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }

    .log-row {
      display: flex;
      gap: 0.84vw; // 18px * 0.0702 * 0.66
      .log-item {
        display: flex;
        align-items: center;
        padding: 0.74vw; // 16px * 0.0702 * 0.66
        flex: 1;
        --ignore-dark-color: #ffffff0a;
        background: var(--ignore-dark-color);

        &:last-child {
          border-bottom: none;
        }

        .log-icon {
          width: 1.49vw; // 32px * 0.0702 * 0.66
          height: 1.39vw; // 30px * 0.0702 * 0.66
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 0.74vw; // 16px * 0.0702 * 0.66
          font-size: max(12px, 0.74vw); // 16px * 0.0702 * 0.66

          &::after {
            content: '';
            display: block;
            width: 1.02vw; // 22px * 0.0702 * 0.66
            height: 1.25vw; // 27px * 0.0702 * 0.66
            background-size: cover;
          }

          &.success {
            &::after {
              --ignore-dark-image: url('/@/assets/images/screen/log-success.png');
              background-image: var(--ignore-dark-image);
            }
          }

          &.error {
            &::after {
              width: 1.49vw; // 32px * 0.0702 * 0.66
              height: 1.37vw; // 29.5px * 0.0702 * 0.66
              --ignore-dark-image: url('/@/assets/images/screen/log-error.png');
              background-image: var(--ignore-dark-image);
              background-size: 100% 100%;
            }
          }

          &.warning {
            &::after {
              --ignore-dark-image: url('/@/assets/images/screen/log-warning.png');
              background-image: var(--ignore-dark-image);
            }
          }
        }

        .log-content {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 1.12vw;
          font-size: max(12px, 0.84vw); // 14px * 0.0702 * 0.66
          .log-action {
            color: rgba(255, 255, 255, 0.7);
          }
          .log-time {
            color: #ffffff;
          }
          .log-tag-box {
            display: flex;
            gap: 3px;
            flex: 1;
            .log-tag {
              margin-right: 0;
              padding: 0px 0.37vw;
              height: 1.12vw;
              line-height: 1.12vw;
              border-radius: 2px;
              border: 1px solid var(--Error-Error6-Normal, #29e8ab);
              color: var(--Error-Error6-Normal, #29e8ab);
              &.warning {
                border-color: #fc8800;
                color: #fc8800;
              }
              &.error {
                border-color: #f53f3f;
                color: #f53f3f;
              }
            }
          }
        }

        .log-status {
          border-radius: 0.09vw; // 2px * 0.0702 * 0.66
          font-size: max(12px, 0.65vw); // 14px * 0.0702 * 0.66
          --ignore-dark-success-color: #29e8ab;
          --ignore-dark-error-color: #f53f3f;
          --ignore-dark-warning-color: #e97a13;

          &::before {
            content: '';
            display: inline-block;
            width: 0.37vw; // 8px * 0.0702 * 0.66
            height: 0.37vw; // 8px * 0.0702 * 0.66
            border-radius: 50%;
            margin-right: 0.18vw; // 4px * 0.0702 * 0.66
            background-color: var(--ignore-dark-success-color);
          }

          &.success {
            color: var(--ignore-dark-success-color);

            &::before {
              background-color: var(--ignore-dark-success-color);
            }
          }

          &.error {
            color: var(--ignore-dark-error-color);

            &::before {
              background-color: var(--ignore-dark-error-color);
            }
          }

          &.warning {
            color: var(--ignore-dark-warning-color);

            &::before {
              background-color: var(--ignore-dark-warning-color);
            }
          }
        }
      }
      &.single-log {
        .log-item {
          width: 100%;
        }
      }
    }

    .cddc-web-empty {
      height: 100%;
    }
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .operation-log {
    gap: 0.6vw;

    .title {
      font-size: max(12px, 0.51vw); // 11px * 0.0702 * 0.66
      margin-bottom: 0.55vh; // 8px * 0.1053 * 0.66
    }

    .log-content {
      padding-right: 0.28vw; // 6px * 0.0702 * 0.66

      &::-webkit-scrollbar {
        width: 0.14vw; // 3px * 0.0702 * 0.66
      }
    }

    .log-item {
      padding: 0.69vh 0.37vw; // (10px * 0.1053 * 0.66) (8px * 0.0702 * 0.66)
      margin-bottom: 0.42vh; // 6px * 0.1053 * 0.66
      font-size: max(12px, 0.46vw); // 10px * 0.0702 * 0.66

      .time {
        margin-right: 0.28vw; // 6px * 0.0702 * 0.66
      }
    }
  }
}
