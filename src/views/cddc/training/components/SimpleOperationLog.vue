<template>
  <div class="simple-operation-log">
    <ScreenTitle>操作日志</ScreenTitle>
    <ScreenBox class="flex-1">
      <div class="log-list box-content">
        <template v-if="logList?.length">
          <div class="log-row" v-for="(item, rowIndex) in logList" :key="rowIndex">
            <div class="log-item">
              <div class="log-icon" :class="getStatusClass(item.result)"></div>
              <div class="log-content">
                <div class="log-main-row">
                  <div class="log-action">{{ getActionText(item) }}</div>
                  <div class="log-status" :class="getStatusClass(item.result)">
                    {{ getStatusText(item.result) }}
                  </div>
                </div>
                <div class="log-time">{{
                  dayjs(item.operationTime).format('YYYY-MM-DD HH:mm:ss')
                }}</div>
              </div>
            </div>
          </div>
        </template>
        <!-- 空数据 -->
        <GeegaEmpty v-else description="暂无数据" />
      </div>
    </ScreenBox>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import ScreenBox from '/@/components/ClientPort/ScreenBox.vue';
import ScreenTitle from '/@/components/ClientPort/ScreenTitle.vue';
import dayjs from 'dayjs';
import { V1LocationStudyOperationLogPagePost } from '/@/api/cddc.req';
import { useAsyncData } from '/@/composables';
import GeegaEmpty from '/@/components/GeegaEmpty/index.vue';
import { watchImmediate } from '@vueuse/core';
import { createLocalStorage } from '/@/utils/cache';

const props = defineProps<{
  detailId?: string;
  data?: any;
}>();

// 本地维护的日志列表
const localLogList = ref<any[]>([]);

// 操作日志
const apiData = useAsyncData(
  async () => {
    if (Object.keys(props.data).length || !props.detailId) return { records: [] };
    const ls = createLocalStorage();
    const locationId = ls.get('locationId');
    if (!locationId) return { records: [] };

    const result = await V1LocationStudyOperationLogPagePost({
      pageSize: 10,
      currentPage: 1,
      data: {
        detailId: props.detailId,
        locationId: locationId,
      },
    });
    // 初始化本地日志列表
    localLogList.value = result.records || [];
    return result;
  },
  { records: [] }
);

watchImmediate(
  () => props.detailId,
  () => apiData.load()
);

// 清空本地日志
const clearLocalLog = () => {
  localLogList.value = [];
};

const logList = computed(() => {
  if (Object.keys(props.data).length && props.data.operationLog) {
    const newLog = props.data.operationLog;
    // 检查新日志是否已存在
    if (!localLogList.value.some((item) => item.id === newLog.id)) {
      localLogList.value = [newLog, ...localLogList.value];
    }
  }
  return localLogList.value;
});

const getStatusClass = (result: number) => {
  return {
    success: result === 1,
    error: result === 0,
    warning: result === 2,
  };
};

const getStatusText = (result: number) => {
  switch (result) {
    case 1:
      return '合格';
    case 0:
      return '不合格';
    case 2:
      return '不达标';
    default:
      return '未知';
  }
};

const getActionText = (item: any) => {
  // 如果是合格的操作，显示"完成一次操作"
  if (item.result === 1) {
    return '完成一次操作';
  }

  // 如果是不合格或不达标，显示具体的错误信息
  // 优先显示 actionName，如果没有则显示 actionLabels 中的描述
  if (item.actionName) {
    return item.actionName;
  }

  if (item.actionLabels && item.actionLabels.length > 0) {
    // 如果有多个标签，用逗号连接
    return item.actionLabels.map((label: any) => label.desc).join('、');
  }

  // 如果都没有，根据状态显示默认信息
  switch (item.result) {
    case 0:
      return '动作不达标：未双手操作';
    case 2:
      return '螺栓未达标';
    default:
      return '操作异常';
  }
};

defineExpose({
  clearLocalLog,
});
</script>

<style lang="less" scoped>
.simple-operation-log {
  display: flex;
  flex-direction: column;
  gap: 0.74vw;
  flex: 1;

  :deep(.screen-box) {
    flex: 1;
    padding: 0.74vw;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 100%;
  }

  .log-list {
    display: flex;
    flex-direction: column;
    gap: 0.28vw; // 紧凑间距
    height: 100%;

    &.box-content {
      display: flex;
      flex-direction: column;
      gap: 0.28vw;
      position: relative;
      z-index: 1;
      overflow-x: hidden;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;

      &::-webkit-scrollbar {
        width: 0.28vw;
        background-color: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 0.14vw;
      }

      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }

    .log-row {
      .log-item {
        display: flex;
        align-items: center; // 改为垂直居中
        padding: 0.56vw 0.74vw; // 紧凑内边距
        --ignore-dark-color: #ffffff0a;
        background: var(--ignore-dark-color);

        .log-icon {
          width: 1.49vw;
          height: 1.39vw;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 0.74vw;
          margin-top: 0.09vw; // 微调对齐
          font-size: max(12px, 0.74vw);
          flex-shrink: 0;

          &::after {
            content: '';
            display: block;
            width: 1.02vw;
            height: 1.25vw;
            background-size: cover;
          }

          &.success {
            &::after {
              --ignore-dark-image: url('/@/assets/images/screen/log-success.png');
              background-image: var(--ignore-dark-image);
            }
          }

          &.error {
            &::after {
              width: 1.49vw;
              height: 1.37vw;
              --ignore-dark-image: url('/@/assets/images/screen/log-error.png');
              background-image: var(--ignore-dark-image);
              background-size: 100% 100%;
            }
          }

          &.warning {
            &::after {
              --ignore-dark-image: url('/@/assets/images/screen/log-warning.png');
              background-image: var(--ignore-dark-image);
            }
          }
        }

        .log-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.18vw;

          .log-main-row {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .log-action {
              font-size: max(12px, 0.74vw);
              color: rgba(255, 255, 255, 0.9);
              line-height: 1.2;
            }

            .log-status {
              font-size: max(10px, 0.56vw);
              padding: 0.09vw 0.37vw;
              border-radius: 2px;
              line-height: 1.2;

              &.success {
                color: #29e8ab;
                border: 1px solid #29e8ab;
              }

              &.error {
                color: #f53f3f;
                border: 1px solid #f53f3f;
              }

              &.warning {
                color: #fc8800;
                border: 1px solid #fc8800;
              }
            }
          }

          .log-time {
            font-size: max(10px, 0.56vw);
            color: rgba(255, 255, 255, 0.6);
            line-height: 1.2;
          }
        }
      }
    }

    .cddc-web-empty {
      height: 100%;
    }
  }
}

// pad尺寸适配
@media screen and (max-width: 1024px) {
  .simple-operation-log {
    gap: 0.6vw;

    .log-list {
      gap: 0.2vw;

      .log-row .log-item {
        padding: 0.4vw 0.6vw;

        .log-content {
          .log-main-row {
            .log-action {
              font-size: max(10px, 0.6vw);
            }

            .log-status {
              font-size: max(8px, 0.45vw);
              padding: 0.05vw 0.25vw;
            }
          }

          .log-time {
            font-size: max(8px, 0.45vw);
          }
        }
      }
    }
  }
}
</style>
